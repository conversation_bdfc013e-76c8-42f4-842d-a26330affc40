# 公司数据爬虫工具 (Company Data Scraper)

这是一个用于收集德国公司信息的免费爬虫工具，专门为公司表单数据收集而设计。

## 功能特点

- 🆓 **完全免费** - 使用开源工具，无需付费API
- 🎯 **针对性爬取** - 专门收集公司表单所需的数据字段
- 🇩🇪 **德国市场** - 针对德国主要招聘网站和公司目录
- 📧 **智能提取** - 自动提取邮箱、电话、网站等联系信息
- 📊 **多格式输出** - 支持JSON、CSV、Excel格式
- 🔄 **可扩展** - 易于添加新的网站和数据源

## 收集的数据字段

根据公司表单需求，工具会收集以下信息：

### 必填字段
- `CompanyName` - 公司名称
- `ContactPerson` - 联系人
- `ContactEmail` - 联系邮箱
- `ContactPhone` - 联系电话
- `NeededPositions` - 需要的职位
- `NumberOfVacancies` - 职位数量
- `WorkLocation` - 工作地点
- `RequiredSkills` - 技能要求

### 可选字段
- `CompanyWebsite` - 公司网站
- `EmploymentModel` - 雇佣模式
- `Urgency` - 紧急程度
- `JobDescription` - 职位描述

## 安装

1. 克隆或下载项目到本地
2. 安装Python依赖：

```bash
cd scraping
pip install -r requirements.txt
```

3. 安装Chrome浏览器（用于Selenium）

## 使用方法

### 1. 按行业爬取

```bash
# 爬取物流行业公司
python src/company_scraper.py --industry logistics --max-companies 50

# 爬取制造业公司
python src/company_scraper.py --industry manufacturing --max-companies 30

# 爬取建筑行业公司
python src/company_scraper.py --industry construction --max-companies 20
```

### 2. 爬取特定URL

```bash
python src/company_scraper.py --urls https://www.kununu.com/de/company1 https://www.xing.com/companies/company2
```

### 3. 指定输出格式

```bash
# 输出为Excel文件
python src/company_scraper.py --industry logistics --format xlsx --output logistics_companies

# 输出为CSV文件
python src/company_scraper.py --industry hospitality --format csv
```

### 4. 使用示例脚本

```bash
python examples/simple_scraper.py
```

## 支持的行业

- `logistics` - 物流运输
- `manufacturing` - 制造业
- `construction` - 建筑业
- `hospitality` - 酒店餐饮
- `healthcare` - 医疗保健
- `retail` - 零售业

## 支持的网站

- **StepStone** - 德国主要招聘网站
- **Indeed德国** - 国际招聘平台德国站
- **XING** - 德国专业社交网络
- **Kununu** - 德国公司评价平台

## 项目结构

```
scraping/
├── src/
│   ├── config/
│   │   └── settings.py          # 配置文件
│   ├── utils/
│   │   ├── web_scraper.py       # 网页爬取工具
│   │   └── data_extractor.py    # 数据提取工具
│   └── company_scraper.py       # 主爬虫脚本
├── examples/
│   └── simple_scraper.py        # 简单示例
├── data/                        # 数据存储目录
├── logs/                        # 日志目录
├── output/                      # 输出文件目录
├── requirements.txt             # Python依赖
└── README.md                    # 说明文档
```

## 配置选项

在 `src/config/settings.py` 中可以调整：

- 请求延迟时间
- 用户代理字符串
- 目标网站配置
- 搜索关键词
- 输出格式设置

## 注意事项

1. **遵守网站条款** - 请确保遵守目标网站的使用条款
2. **合理使用** - 设置适当的请求延迟，避免对网站造成负担
3. **数据验证** - 爬取的数据可能需要人工验证和清理
4. **法律合规** - 确保数据使用符合GDPR等相关法规

## 故障排除

### 常见问题

1. **Chrome驱动问题**
   ```bash
   # 手动安装Chrome驱动
   pip install --upgrade webdriver-manager
   ```

2. **网络连接问题**
   - 检查网络连接
   - 尝试使用VPN
   - 调整请求超时设置

3. **数据提取失败**
   - 网站可能更新了结构
   - 检查日志文件了解详细错误
   - 尝试使用Selenium模式

## 扩展开发

### 添加新网站

1. 在 `settings.py` 的 `TARGET_SITES` 中添加配置
2. 实现对应的选择器和URL构建逻辑
3. 测试数据提取效果

### 添加新数据字段

1. 在 `data_extractor.py` 中添加提取逻辑
2. 更新 `COMPANY_FIELDS` 映射
3. 修改输出结构

## 许可证

本项目采用MIT许可证，可自由使用和修改。

## 支持

如有问题或建议，请创建Issue或联系开发团队。
