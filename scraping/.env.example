# Environment configuration for company scraper

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Scraping settings
REQUEST_DELAY=1
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# Selenium settings
SELENIUM_TIMEOUT=10
HEADLESS_BROWSER=true

# Output settings
DEFAULT_OUTPUT_FORMAT=json

# Optional: Proxy settings (if needed)
# HTTP_PROXY=http://proxy:port
# HTTPS_PROXY=https://proxy:port

# Optional: Custom user agents (comma separated)
# CUSTOM_USER_AGENTS=Mozilla/5.0...,Mozilla/5.0...

# Optional: Database connection (for direct storage)
# DATABASE_URL=postgresql://user:pass@localhost/db
