"""
Simple example script for scraping company data
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from company_scraper import CompanyScraper

def main():
    """Simple example of using the company scraper"""
    
    print("🔍 Starting Company Data Scraper")
    print("=" * 50)
    
    # Initialize scraper
    scraper = CompanyScraper()
    
    try:
        # Example 1: Scrape logistics companies
        print("\n📦 Scraping logistics companies...")
        logistics_companies = scraper.scrape_by_industry('logistics', max_companies=10)
        
        if logistics_companies:
            print(f"✅ Found {len(logistics_companies)} logistics companies")
            for company in logistics_companies[:3]:  # Show first 3
                print(f"  - {company['CompanyName']} ({company['WorkLocation']})")
        
        # Example 2: Scrape specific URLs (if you have them)
        print("\n🌐 Example: Scraping specific URLs...")
        example_urls = [
            "https://www.kununu.com/de/dhl",
            "https://www.xing.com/companies/amazonlogistikgmbh"
        ]
        
        # Note: These are example URLs - they may not work without proper handling
        # specific_companies = scraper.scrape_specific_urls(example_urls)
        
        # Save all scraped data
        print("\n💾 Saving data...")
        filepath = scraper.save_data('example_companies', 'json')
        
        if filepath:
            print(f"✅ Data saved to: {filepath}")
        
        # Show summary
        summary = scraper.get_summary()
        print("\n📊 Summary:")
        print(f"  Total companies: {summary['total']}")
        print(f"  With email: {summary['with_email']}")
        print(f"  With phone: {summary['with_phone']}")
        print(f"  With website: {summary['with_website']}")
        
        if summary.get('by_industry'):
            print("  By industry:")
            for industry, count in summary['by_industry'].items():
                print(f"    {industry}: {count}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        scraper.cleanup()
        print("\n🏁 Scraping completed!")

if __name__ == "__main__":
    main()
