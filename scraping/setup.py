#!/usr/bin/env python3
"""
Setup script for company scraper
"""
import os
import sys
import subprocess
from pathlib import Path

def run_command(command):
    """Run a shell command and return success status"""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {command}")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Setup the scraping environment"""
    print("🚀 Setting up Company Data Scraper")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Create directories
    directories = ['data', 'logs', 'output']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    # Install requirements
    print("\n📦 Installing Python packages...")
    if not run_command("pip install -r requirements.txt"):
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            run_command("cp .env.example .env")
            print("✅ Created .env file from example")
        else:
            print("⚠️  No .env.example found, skipping .env creation")
    
    # Test Chrome driver installation
    print("\n🌐 Testing Chrome driver...")
    test_script = """
import sys
sys.path.append('src')
try:
    from webdriver_manager.chrome import ChromeDriverManager
    ChromeDriverManager().install()
    print("✅ Chrome driver installed successfully")
except Exception as e:
    print(f"❌ Chrome driver installation failed: {e}")
"""
    
    try:
        exec(test_script)
    except Exception as e:
        print(f"⚠️  Chrome driver test failed: {e}")
        print("You may need to install Chrome browser manually")
    
    print("\n🎉 Setup completed!")
    print("\nNext steps:")
    print("1. Review and edit .env file if needed")
    print("2. Run example: python examples/simple_scraper.py")
    print("3. Or run main scraper: python src/company_scraper.py --industry logistics")

if __name__ == "__main__":
    main()
