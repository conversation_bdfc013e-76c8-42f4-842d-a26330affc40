"""
Web scraping utilities using requests and BeautifulSoup
"""
import requests
import time
import random
import logging
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

from ..config.settings import USER_AGENTS, REQUEST_DELAY, REQUEST_TIMEOUT, MAX_RETRIES, SELENIUM_TIMEOUT, HEADLESS_BROWSER

logger = logging.getLogger(__name__)

class WebScraper:
    """Web scraper using requests and BeautifulSoup with Selenium fallback"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': random.choice(USER_AGENTS),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        self.driver = None
    
    def get_page(self, url: str, use_selenium: bool = False) -> Optional[BeautifulSoup]:
        """Fetch and parse a web page"""
        if use_selenium:
            return self._get_page_selenium(url)
        else:
            return self._get_page_requests(url)
    
    def _get_page_requests(self, url: str) -> Optional[BeautifulSoup]:
        """Fetch page using requests"""
        for attempt in range(MAX_RETRIES):
            try:
                logger.info(f"Fetching {url} (attempt {attempt + 1})")
                
                response = self.session.get(url, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()
                
                # Parse with BeautifulSoup
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Add delay between requests
                time.sleep(REQUEST_DELAY + random.uniform(0, 1))
                
                return soup
                
            except requests.RequestException as e:
                logger.warning(f"Request failed for {url}: {e}")
                if attempt == MAX_RETRIES - 1:
                    logger.error(f"Failed to fetch {url} after {MAX_RETRIES} attempts")
                    return None
                time.sleep(2 ** attempt)  # Exponential backoff
        
        return None
    
    def _get_page_selenium(self, url: str) -> Optional[BeautifulSoup]:
        """Fetch page using Selenium (for JavaScript-heavy sites)"""
        try:
            if not self.driver:
                self._init_selenium_driver()
            
            logger.info(f"Fetching {url} with Selenium")
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, SELENIUM_TIMEOUT).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Get page source and parse
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            time.sleep(REQUEST_DELAY)
            return soup
            
        except Exception as e:
            logger.error(f"Selenium failed for {url}: {e}")
            return None
    
    def _init_selenium_driver(self):
        """Initialize Selenium Chrome driver"""
        try:
            chrome_options = Options()
            if HEADLESS_BROWSER:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument(f"--user-agent={random.choice(USER_AGENTS)}")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
        except Exception as e:
            logger.error(f"Failed to initialize Selenium driver: {e}")
            raise
    
    def search_companies(self, site_config: Dict, keywords: List[str], max_pages: int = 5) -> List[str]:
        """Search for company URLs on a job site"""
        company_urls = []
        base_url = site_config['base_url']
        
        for keyword in keywords:
            logger.info(f"Searching for '{keyword}' on {base_url}")
            
            # Construct search URL (site-specific logic needed)
            search_url = self._build_search_url(base_url, keyword)
            
            for page in range(1, max_pages + 1):
                page_url = f"{search_url}&page={page}"
                soup = self.get_page(page_url)
                
                if not soup:
                    break
                
                # Extract company/job URLs
                urls = self._extract_company_urls(soup, site_config, base_url)
                company_urls.extend(urls)
                
                if not urls:  # No more results
                    break
        
        return list(set(company_urls))  # Remove duplicates
    
    def _build_search_url(self, base_url: str, keyword: str) -> str:
        """Build search URL for different job sites"""
        if 'stepstone.de' in base_url:
            return f"{base_url}/jobs/suche?q={keyword}&radius=50"
        elif 'indeed.com' in base_url:
            return f"{base_url}/jobs?q={keyword}&l=Deutschland"
        elif 'xing.com' in base_url:
            return f"{base_url}/jobs/search?keywords={keyword}"
        elif 'kununu.com' in base_url:
            return f"{base_url}/de/suche/unternehmen?q={keyword}"
        else:
            return f"{base_url}/search?q={keyword}"
    
    def _extract_company_urls(self, soup: BeautifulSoup, site_config: Dict, base_url: str) -> List[str]:
        """Extract company URLs from search results"""
        urls = []
        
        # Site-specific selectors for company links
        if 'stepstone.de' in base_url:
            links = soup.select('a[href*="/unternehmen/"], a[href*="/jobs/"]')
        elif 'indeed.com' in base_url:
            links = soup.select('a[href*="/cmp/"], a[href*="/viewjob"]')
        elif 'xing.com' in base_url:
            links = soup.select('a[href*="/companies/"], a[href*="/jobs/"]')
        elif 'kununu.com' in base_url:
            links = soup.select('a[href*="/de/"]')
        else:
            links = soup.select('a[href]')
        
        for link in links:
            href = link.get('href')
            if href:
                full_url = urljoin(base_url, href)
                urls.append(full_url)
        
        return urls[:20]  # Limit results per page
    
    def scrape_company_data(self, url: str, site_config: Dict) -> Dict[str, Any]:
        """Scrape company data from a specific URL"""
        soup = self.get_page(url)
        if not soup:
            return {}
        
        data = {
            'source_url': url,
            'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        selectors = site_config.get('selectors', {})
        
        # Extract company name
        if 'company_name' in selectors:
            elements = soup.select(selectors['company_name'])
            if elements:
                data['company_name'] = elements[0].get_text(strip=True)
        
        # Extract location
        if 'location' in selectors:
            elements = soup.select(selectors['location'])
            if elements:
                data['location'] = elements[0].get_text(strip=True)
        
        # Extract description
        if 'description' in selectors:
            elements = soup.select(selectors['description'])
            if elements:
                data['description'] = ' '.join([el.get_text(strip=True) for el in elements[:3]])
        
        # Extract website
        if 'website' in selectors:
            elements = soup.select(selectors['website'])
            if elements:
                data['website'] = elements[0].get('href', '')
        
        # Extract all text for email/phone extraction
        data['full_text'] = soup.get_text()
        
        return data
    
    def close(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
        self.session.close()
