"""
Utility functions for extracting and cleaning company data
"""
import re
import logging
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse
import phonenumbers
from email_validator import validate_email, EmailNotValidError

logger = logging.getLogger(__name__)

class DataExtractor:
    """Extract and clean company data from scraped content"""
    
    def __init__(self):
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(\+49|0)[0-9\s\-\(\)]{8,20}')
        self.website_pattern = re.compile(r'https?://[^\s<>"]+|www\.[^\s<>"]+')
    
    def extract_emails(self, text: str) -> List[str]:
        """Extract valid email addresses from text"""
        if not text:
            return []
        
        emails = self.email_pattern.findall(text)
        valid_emails = []
        
        for email in emails:
            try:
                validated = validate_email(email)
                valid_emails.append(validated.email)
            except EmailNotValidError:
                continue
        
        return list(set(valid_emails))  # Remove duplicates
    
    def extract_phone_numbers(self, text: str) -> List[str]:
        """Extract and format German phone numbers"""
        if not text:
            return []
        
        phones = self.phone_pattern.findall(text)
        formatted_phones = []
        
        for phone in phones:
            try:
                # Parse as German number
                parsed = phonenumbers.parse(phone, "DE")
                if phonenumbers.is_valid_number(parsed):
                    formatted = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL)
                    formatted_phones.append(formatted)
            except phonenumbers.NumberParseException:
                # Keep original if parsing fails but looks like a phone number
                cleaned = re.sub(r'[^\d+]', '', phone)
                if len(cleaned) >= 10:
                    formatted_phones.append(phone)
        
        return list(set(formatted_phones))
    
    def extract_websites(self, text: str, base_url: str = None) -> List[str]:
        """Extract website URLs from text"""
        if not text:
            return []
        
        urls = self.website_pattern.findall(text)
        cleaned_urls = []
        
        for url in urls:
            # Clean up the URL
            url = url.strip('.,;!?')
            
            # Add protocol if missing
            if url.startswith('www.'):
                url = 'https://' + url
            
            # Make absolute URL if base_url provided
            if base_url and not url.startswith(('http://', 'https://')):
                url = urljoin(base_url, url)
            
            # Validate URL format
            try:
                parsed = urlparse(url)
                if parsed.netloc:
                    cleaned_urls.append(url)
            except Exception:
                continue
        
        return list(set(cleaned_urls))
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""
        
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove common HTML entities
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        
        return text
    
    def extract_company_name(self, soup, selectors: List[str]) -> Optional[str]:
        """Extract company name using multiple selectors"""
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = self.clean_text(element.get_text())
                if text and len(text) > 2:
                    return text
        return None
    
    def extract_location(self, text: str) -> Optional[str]:
        """Extract location information"""
        if not text:
            return None
        
        # German city patterns
        german_cities = [
            'Berlin', 'Hamburg', 'München', 'Köln', 'Frankfurt', 'Stuttgart',
            'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig', 'Bremen', 'Dresden',
            'Hannover', 'Nürnberg', 'Duisburg', 'Bochum', 'Wuppertal', 'Bielefeld'
        ]
        
        text_clean = self.clean_text(text)
        
        # Look for German cities
        for city in german_cities:
            if city.lower() in text_clean.lower():
                return text_clean
        
        # Look for postal codes (German format)
        postal_pattern = re.compile(r'\b\d{5}\s+[A-Za-zäöüß\s]+\b')
        postal_match = postal_pattern.search(text_clean)
        if postal_match:
            return postal_match.group()
        
        return text_clean if len(text_clean) > 3 else None
    
    def extract_job_positions(self, text: str) -> List[str]:
        """Extract job positions from text"""
        if not text:
            return []
        
        # Common job titles in German
        job_keywords = [
            'mitarbeiter', 'arbeiter', 'fachkraft', 'spezialist', 'manager',
            'leiter', 'assistent', 'sachbearbeiter', 'techniker', 'ingenieur',
            'verkäufer', 'berater', 'entwickler', 'fahrer', 'lagerist'
        ]
        
        positions = []
        text_lower = text.lower()
        
        for keyword in job_keywords:
            if keyword in text_lower:
                # Extract surrounding context
                pattern = rf'\b[\w\s]*{keyword}[\w\s]*\b'
                matches = re.findall(pattern, text_lower, re.IGNORECASE)
                positions.extend([match.strip() for match in matches if len(match.strip()) > 5])
        
        return list(set(positions))
    
    def structure_company_data(self, raw_data: Dict[str, Any]) -> Dict[str, str]:
        """Structure raw scraped data into company form format"""
        structured = {
            'CompanyName': '',
            'ContactPerson': '',
            'ContactEmail': '',
            'ContactPhone': '',
            'CompanyWebsite': '',
            'NeededPositions': '',
            'NumberOfVacancies': '',
            'WorkLocation': '',
            'RequiredSkills': '',
            'EmploymentModel': '',
            'Urgency': '',
            'JobDescription': '',
            'Source': raw_data.get('source_url', ''),
            'ScrapedAt': raw_data.get('scraped_at', '')
        }
        
        # Map raw data to structured fields
        if 'company_name' in raw_data:
            structured['CompanyName'] = self.clean_text(raw_data['company_name'])
        
        if 'emails' in raw_data and raw_data['emails']:
            structured['ContactEmail'] = raw_data['emails'][0]
        
        if 'phones' in raw_data and raw_data['phones']:
            structured['ContactPhone'] = raw_data['phones'][0]
        
        if 'websites' in raw_data and raw_data['websites']:
            structured['CompanyWebsite'] = raw_data['websites'][0]
        
        if 'location' in raw_data:
            structured['WorkLocation'] = self.clean_text(raw_data['location'])
        
        if 'description' in raw_data:
            structured['JobDescription'] = self.clean_text(raw_data['description'])
        
        if 'positions' in raw_data:
            structured['NeededPositions'] = ', '.join(raw_data['positions'][:3])  # Top 3 positions
        
        return structured
