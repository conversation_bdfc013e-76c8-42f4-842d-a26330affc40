"""
Main company data scraper
"""
import json
import csv
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import pandas as pd

from config.settings import TARGET_SITES, SEARCH_KEYWORDS, OUTPUT_DIR, LOG_LEVEL, LOG_FORMAT
from utils.web_scraper import WebScraper
from utils.data_extractor import DataExtractor

# Configure logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class CompanyScraper:
    """Main scraper class for collecting company data"""
    
    def __init__(self):
        self.scraper = WebScraper()
        self.extractor = DataExtractor()
        self.scraped_companies = []
    
    def scrape_by_industry(self, industry: str, max_companies: int = 50) -> List[Dict[str, Any]]:
        """Scrape companies by industry"""
        if industry not in SEARCH_KEYWORDS:
            logger.error(f"Unknown industry: {industry}")
            return []
        
        keywords = SEARCH_KEYWORDS[industry]
        logger.info(f"Starting scrape for {industry} industry with keywords: {keywords}")
        
        all_companies = []
        
        for site_name, site_config in TARGET_SITES.items():
            logger.info(f"Scraping {site_name}...")
            
            try:
                # Search for company URLs
                company_urls = self.scraper.search_companies(site_config, keywords, max_pages=3)
                logger.info(f"Found {len(company_urls)} URLs on {site_name}")
                
                # Scrape each company
                for i, url in enumerate(company_urls[:max_companies//len(TARGET_SITES)]):
                    if len(all_companies) >= max_companies:
                        break
                    
                    logger.info(f"Scraping company {i+1}/{len(company_urls)}: {url}")
                    
                    # Get raw data
                    raw_data = self.scraper.scrape_company_data(url, site_config)
                    if not raw_data:
                        continue
                    
                    # Extract structured data
                    company_data = self._process_company_data(raw_data)
                    if company_data and company_data['CompanyName']:
                        company_data['Industry'] = industry
                        company_data['SourceSite'] = site_name
                        all_companies.append(company_data)
                        logger.info(f"Successfully scraped: {company_data['CompanyName']}")
            
            except Exception as e:
                logger.error(f"Error scraping {site_name}: {e}")
                continue
        
        logger.info(f"Completed scraping. Found {len(all_companies)} companies")
        self.scraped_companies.extend(all_companies)
        return all_companies
    
    def scrape_specific_urls(self, urls: List[str]) -> List[Dict[str, Any]]:
        """Scrape specific company URLs"""
        companies = []
        
        for i, url in enumerate(urls):
            logger.info(f"Scraping URL {i+1}/{len(urls)}: {url}")
            
            # Determine site config based on URL
            site_config = self._get_site_config_for_url(url)
            
            # Get raw data
            raw_data = self.scraper.scrape_company_data(url, site_config)
            if not raw_data:
                continue
            
            # Extract structured data
            company_data = self._process_company_data(raw_data)
            if company_data and company_data['CompanyName']:
                companies.append(company_data)
                logger.info(f"Successfully scraped: {company_data['CompanyName']}")
        
        self.scraped_companies.extend(companies)
        return companies
    
    def _process_company_data(self, raw_data: Dict[str, Any]) -> Dict[str, str]:
        """Process raw scraped data into structured company data"""
        # Extract emails and phones from full text
        full_text = raw_data.get('full_text', '')
        raw_data['emails'] = self.extractor.extract_emails(full_text)
        raw_data['phones'] = self.extractor.extract_phone_numbers(full_text)
        raw_data['websites'] = self.extractor.extract_websites(full_text, raw_data.get('source_url'))
        raw_data['positions'] = self.extractor.extract_job_positions(full_text)
        
        # Structure the data
        return self.extractor.structure_company_data(raw_data)
    
    def _get_site_config_for_url(self, url: str) -> Dict:
        """Get site configuration based on URL"""
        for site_name, config in TARGET_SITES.items():
            if config['base_url'].replace('https://', '').replace('www.', '') in url:
                return config
        
        # Default config for unknown sites
        return {
            'selectors': {
                'company_name': 'h1, .company-name, .title',
                'location': '.location, .address',
                'description': '.description, .about, p',
                'website': 'a[href*="http"]'
            }
        }
    
    def save_data(self, filename: str = None, format: str = 'json') -> str:
        """Save scraped data to file"""
        if not self.scraped_companies:
            logger.warning("No data to save")
            return ""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if not filename:
            filename = f"companies_{timestamp}"
        
        filepath = OUTPUT_DIR / f"{filename}.{format}"
        
        try:
            if format == 'json':
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(self.scraped_companies, f, indent=2, ensure_ascii=False)
            
            elif format == 'csv':
                if self.scraped_companies:
                    df = pd.DataFrame(self.scraped_companies)
                    df.to_csv(filepath, index=False, encoding='utf-8')
            
            elif format == 'xlsx':
                if self.scraped_companies:
                    df = pd.DataFrame(self.scraped_companies)
                    df.to_excel(filepath, index=False)
            
            logger.info(f"Data saved to {filepath}")
            return str(filepath)
        
        except Exception as e:
            logger.error(f"Error saving data: {e}")
            return ""
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of scraped data"""
        if not self.scraped_companies:
            return {"total": 0}
        
        df = pd.DataFrame(self.scraped_companies)
        
        summary = {
            "total": len(self.scraped_companies),
            "with_email": len(df[df['ContactEmail'] != '']),
            "with_phone": len(df[df['ContactPhone'] != '']),
            "with_website": len(df[df['CompanyWebsite'] != '']),
            "by_industry": df.groupby('Industry').size().to_dict() if 'Industry' in df.columns else {},
            "by_source": df.groupby('SourceSite').size().to_dict() if 'SourceSite' in df.columns else {}
        }
        
        return summary
    
    def cleanup(self):
        """Clean up resources"""
        self.scraper.close()

def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description='Scrape company data for job matching')
    parser.add_argument('--industry', choices=list(SEARCH_KEYWORDS.keys()), 
                       help='Industry to scrape')
    parser.add_argument('--urls', nargs='+', help='Specific URLs to scrape')
    parser.add_argument('--max-companies', type=int, default=50, 
                       help='Maximum number of companies to scrape')
    parser.add_argument('--output', default='companies', help='Output filename')
    parser.add_argument('--format', choices=['json', 'csv', 'xlsx'], default='json',
                       help='Output format')
    
    args = parser.parse_args()
    
    scraper = CompanyScraper()
    
    try:
        if args.industry:
            companies = scraper.scrape_by_industry(args.industry, args.max_companies)
        elif args.urls:
            companies = scraper.scrape_specific_urls(args.urls)
        else:
            logger.error("Please specify either --industry or --urls")
            return
        
        # Save data
        if companies:
            filepath = scraper.save_data(args.output, args.format)
            
            # Print summary
            summary = scraper.get_summary()
            print(f"\nScraping completed!")
            print(f"Total companies: {summary['total']}")
            print(f"With email: {summary['with_email']}")
            print(f"With phone: {summary['with_phone']}")
            print(f"With website: {summary['with_website']}")
            print(f"Data saved to: {filepath}")
        else:
            print("No companies found")
    
    finally:
        scraper.cleanup()

if __name__ == "__main__":
    main()
