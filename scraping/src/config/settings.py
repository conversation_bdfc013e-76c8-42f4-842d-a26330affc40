"""
Configuration settings for company data scraping
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base directories
BASE_DIR = Path(__file__).parent.parent.parent
DATA_DIR = BASE_DIR / "data"
LOGS_DIR = BASE_DIR / "logs"
OUTPUT_DIR = BASE_DIR / "output"

# Create directories if they don't exist
for directory in [DATA_DIR, LOGS_DIR, OUTPUT_DIR]:
    directory.mkdir(exist_ok=True)

# Scraping settings
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
]

# Request settings
REQUEST_DELAY = 1  # seconds between requests
REQUEST_TIMEOUT = 30  # seconds
MAX_RETRIES = 3

# Selenium settings
SELENIUM_TIMEOUT = 10
HEADLESS_BROWSER = True

# German job sites and company directories (free sources)
TARGET_SITES = {
    'xing': {
        'base_url': 'https://www.xing.com',
        'search_patterns': ['/companies/', '/jobs/'],
        'selectors': {
            'company_name': '.company-name, h1, .profile-company-name',
            'contact_info': '.contact-info, .company-contact',
            'description': '.company-description, .about-company',
            'location': '.location, .company-location',
            'website': 'a[href*="http"]'
        }
    },
    'stepstone': {
        'base_url': 'https://www.stepstone.de',
        'search_patterns': ['/jobs/', '/unternehmen/'],
        'selectors': {
            'company_name': '.company-name, h2, .at-listing-item-company-name',
            'location': '.location, .at-listing-item-location',
            'description': '.job-description, .company-description'
        }
    },
    'indeed': {
        'base_url': 'https://de.indeed.com',
        'search_patterns': ['/jobs', '/cmp/'],
        'selectors': {
            'company_name': '.companyName, [data-testid="company-name"]',
            'location': '.companyLocation, [data-testid="job-location"]',
            'description': '.jobsearch-jobDescriptionText, .jobsearch-JobComponent-description'
        }
    },
    'kununu': {
        'base_url': 'https://www.kununu.com',
        'search_patterns': ['/de/'],
        'selectors': {
            'company_name': '.company-profile-name, h1',
            'location': '.company-profile-location, .location',
            'description': '.company-profile-description',
            'website': '.company-profile-website a'
        }
    }
}

# Company data fields mapping
COMPANY_FIELDS = {
    'CompanyName': ['company_name', 'name', 'title'],
    'ContactPerson': ['contact_person', 'contact', 'hr_contact'],
    'ContactEmail': ['email', 'contact_email', 'hr_email'],
    'ContactPhone': ['phone', 'contact_phone', 'telephone'],
    'CompanyWebsite': ['website', 'url', 'homepage'],
    'WorkLocation': ['location', 'address', 'city'],
    'RequiredSkills': ['skills', 'requirements', 'qualifications'],
    'JobDescription': ['description', 'about', 'job_description'],
    'NeededPositions': ['positions', 'jobs', 'vacancies'],
    'NumberOfVacancies': ['vacancy_count', 'openings', 'positions_count']
}

# Search keywords for different industries
SEARCH_KEYWORDS = {
    'logistics': ['logistik', 'lager', 'transport', 'spedition', 'warehouse'],
    'manufacturing': ['produktion', 'fertigung', 'industrie', 'manufacturing'],
    'construction': ['bau', 'construction', 'handwerk', 'bauunternehmen'],
    'hospitality': ['hotel', 'restaurant', 'gastronomie', 'hospitality'],
    'healthcare': ['pflege', 'gesundheit', 'healthcare', 'medical'],
    'retail': ['einzelhandel', 'retail', 'verkauf', 'sales']
}

# Output formats
OUTPUT_FORMATS = ['json', 'csv', 'xlsx']
DEFAULT_OUTPUT_FORMAT = 'json'

# Logging configuration
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
