{"common": {"required": "必填哦～", "submit": "提交", "submitting": "提交中…请稍等", "success": "操作成功！", "error": "出错啦", "warning": "注意一下", "loading": "正在加载…", "select_placeholder": "请选择", "language": "语言", "optional": "选填", "yes": "是的", "no": "不是", "remove": "移除"}, "navigation": {"home": "首页", "privacy_policy": "隐私政策", "terms_of_service": "服务条款", "toggle_hiring": "我是雇主", "toggle_job_seeking": "我是求职者", "toggle_hiring_short": "雇主", "toggle_job_seeking_short": "求职", "blog": "博客"}, "hero": {"title": "蓝领找工新体验", "title_powered": "AI 帮你省心", "subtitle": "不用折腾文书，轻松找到好工作", "subtitle_2": "快、省心，支持多语言", "cta_worker": "免费注册", "cta_company": "发布职位", "trust_indicator": "2,500+位求职者信赖", "employer": {"title": "帮你找到靠谱员工", "title_powered": "AI 智能推荐", "subtitle": "一键联系本地1000+经过核实的蓝领工人，招聘不再头疼！", "subtitle_2": "高效、省心、质量有保证", "cta_worker": "发布招聘", "cta_company": "了解详情"}}, "features": {"title": "为什么用 <PERSON><PERSON>us？", "subtitle": "让工作匹配更简单高效", "section_title": "选择 Sohnus 的理由", "section_subtitle": "看看我们的 AI 平台怎么让求职/招聘更省心", "feature1_title": "聊天就能搞定简历", "feature1_description": "你说你的经历，我们帮你生成标准简历，整个流程超简单", "feature2_title": "自动匹配好工作", "feature2_description": "不再刷屏找岗位，AI 帮你找最合适，还能帮你一键投递", "feature3_title": "越用越值钱", "feature3_description": "工作经验越多，推给你的都是薪水高、更稳定、发展好的职位", "feature4_title": "专业顾问在线帮", "feature4_description": "简历、面试、签约都有专业团队陪跑，有问题随时问", "employer": {"title": "雇主专属优势", "subtitle": "蓝领招聘变轻松，招人不再难", "feature1_title": "AI 智能推荐", "feature1_description": "AI 自动分析需求，帮你一秒筛出最匹配的工人", "feature2_title": "极速招聘", "feature2_description": "招聘周期缩短到几天，24小时收到靠谱简历", "feature3_title": "已核实工人", "feature3_description": "所有工人都严格核查身份和技能，放心雇用", "feature4_title": "招聘数据全掌握", "feature4_description": "数据分析一目了然，招聘成效心里有数"}}, "forms": {"title": "马上加入我们", "subtitle": "选一个身份：你是在找工作还是找员工？", "tab_worker": "找工作", "tab_company": "找员工", "required_note": "此项为必填", "footer_agreement": "提交即表示你同意", "and": "和"}, "chatbot": {"title": "AI 聊天助手", "chat_with_us": "有问题聊一聊", "chat": "在线咨询"}, "worker_form": {"title": "求职信息填写", "subtitle": "填写后我们会自动帮你生成简历", "success_title": "申请已提交！", "success_description": "我们会尽快联系你，请注意查收邮件。", "personal_info": "个人信息", "job_expectations": "求职意向", "work_history": "工作经历", "skills_qualifications": "技能证书", "fields": {"first_name": "名", "last_name": "姓", "email": "邮箱", "phone": "手机号", "connect_whatsapp": "加 WhatsApp 联系", "facebook": "Facebook 账号", "connect_facebook": "用 Facebook 联系", "wechat": "微信号", "connect_wechat": "进微信群", "city": "现居城市", "nationality": "国籍", "avatar": "个人照片", "availability_date": "最快到岗日期", "willing_to_travel": "愿意出差吗", "salary_expectation": "期望月薪 (€)", "desired_position": "想应聘的职位", "employment_type": "求职类型", "job_title": "职位名称", "job_company": "公司名", "job_start": "开始时间", "job_end": "结束时间", "job_duration": "工作年限", "job_tasks": "主要职责", "job1_title": "最近职位", "job1_company": "最近公司", "job1_duration": "工作年限", "job1_tasks": "主要职责", "education_level": "学历", "education_start": "入学时间", "education_end": "毕业时间", "education_school": "学校/大学", "education_detail": "专业/学位", "german_level": "德语水平", "other_languages": "其他语言", "driving_license": "驾照", "skills_certs": "其他技能或证书"}, "job_entry": "工作经历", "add_job": "添加经历", "education_entry": "教育经历", "add_education": "添加教育", "education_details": "教育详情", "avatar_help": "请上传清晰的证件照（JPG/PNG，2MB以内）", "avatar_required": "请上传个人照片", "avatar_invalid_format": "只支持 JPG、PNG 格式的图片", "avatar_too_large": "图片大小需小于2MB", "avatar_too_small": "图片太小，请换张清晰点的", "placeholders": {"first_name": "输入名字", "last_name": "输入姓氏", "email": "<EMAIL>", "phone": "+49 **********", "facebook": "facebook.com/用户名 或 @你的账号", "wechat": "你的微信号", "city": "如：柏林", "nationality": "如：中国，德国，波兰", "willing_to_travel": "请选择", "salary_expectation": "如：2500", "desired_position": "如：仓库工人", "employment_type": "请选择类型", "job_title": "如：拣货员", "job_company": "如：亚马逊物流", "job_duration": "如：2年", "job_tasks": "说说主要做什么…（最多300字）", "job1_title": "如：拣货员", "job1_company": "如：亚马逊物流", "job1_duration": "如：2年", "job1_tasks": "说说主要做什么…（最多300字）", "education_level": "请选择学历", "education_school": "如：北京大学", "education_detail": "如：计算机本科，工程硕士…（最多200字）", "german_level": "请选择德语水平", "other_languages": "如：英语（C1），西班牙语（A2）（最多100字）", "driving_license": "请选择驾照类型", "skills_certs": "如：焊工证书，SCC证书，MS Office…（最多250字）", "availability_date": "选择到岗日期", "job_start_month": "开始月份", "job_end_month": "结束月份", "education_start_month": "入学月份", "education_end_month": "毕业月份"}, "options": {"employment_type": {"full_time": "全职", "part_time": "兼职", "contract": "合同工", "temporary": "临时工", "internship": "实习"}, "education_level": {"no_degree": "无学历", "vocational_training": "职业培训", "high_school": "高中毕业", "bachelors_degree": "本科", "masters_degree": "硕士", "phd": "博士"}, "german_level": {"a1": "A1（入门）", "a2": "A2（基础）", "b1": "B1（一般）", "b2": "B2（较好）", "c1": "C1（流利）", "c2": "C2（母语）", "native_speaker": "德语母语"}, "driving_license": {"none": "没驾照", "class_b": "B类（小轿车）", "class_c1": "C1类（小卡车）", "class_ce": "CE类（拖挂卡车）", "forklift_license": "叉车证"}}}, "team": {"title": "我们的团队", "subtitle": "一群跨界工程师和专业顾问，专为打工人服务！", "hero_title": "大家一起搞事业", "hero_subtitle": "我们是你的后盾，陪你一起前行", "members": {"roger": {"name": "<PERSON>", "role": "CEO&创始人", "bio": "工程师出身+管理背景，Roger 致力用AI让找工作、招人都省心，不用再被语言卡住。"}, "bijun": {"name": "Dr. <PERSON><PERSON><PERSON>", "role": "CTO&联合创始人", "bio": "工程博士+系统经验，Bijun 负责技术方案，平台AI自动帮你配对，不懂德语也不怕。"}, "yuanwei": {"name": "<PERSON><PERSON> Fang", "role": "CPO&联合创始人", "bio": "UX 设计出身，Yuanwei 让每个界面都好用易懂，真实用户导向，做德国蓝领的贴心助手。"}}}, "footer": {"company_description": "让每个蓝领都能找到靠谱工作，AI 帮你省时省力。", "job_seekers": "找工作", "employers": "雇主招人", "contact_info": "联系方式", "legal": "法律信息", "apply_now": "马上申请", "post_jobs": "发布职位", "privacy_policy": "隐私政策", "terms_of_service": "服务条款", "copyright": "© 2025 Sohnus. 版权所有。"}, "company_form": {"title": "公司咨询入口", "subtitle": "告诉我们你的用人需求", "success_title": "已收到你的需求！", "success_description": "我们会尽快联系你，也会发邮件确认。", "fields": {"company_name": "公司名称", "contact_person": "联系人", "contact_email": "联系邮箱", "contact_phone": "联系电话", "company_website": "公司网址", "needed_positions": "招聘职位", "number_of_vacancies": "人数", "work_location": "工作地点", "required_skills": "必备技能", "employment_model": "用工类型", "urgency": "紧急程度", "job_description": "职位详情"}, "placeholders": {"company_name": "公司名称", "contact_person": "全名", "contact_email": "<EMAIL>", "contact_phone": "+49 **********", "company_website": "https://www.company.com", "needed_positions": "如：仓库工人、叉车司机", "number_of_vacancies": "如：5", "work_location": "如：柏林/远程", "required_skills": "如：叉车证、德语B2、能吃苦", "employment_model": "请选择类型", "urgency": "请选择紧急程度", "job_description": "写清工作内容、环境、福利等"}, "options": {"employment_model": {"full_time": "全职", "part_time": "兼职", "contract": "合同工", "temporary": "临时工", "internship": "实习"}, "urgency": {"immediate": "越快越好（1周内）", "urgent": "比较急（2-4周）", "normal": "正常（1-2个月）", "flexible": "不着急，合适就招"}}}, "welcome_modal": {"text_over_image": "从这里开启新体验", "title": "欢迎来到 Sohnus！", "description": "选你的身份，马上用 AI 帮你找工/招人", "job_seeker_button": "我是找工作的", "hiring_button": "我们要招人", "additional_info": "以后可以随时在资料里改身份哦", "close_button": "关闭"}, "blog": {"latest_posts": "最新文章", "view_all": "全部文章", "loading": "博客加载中…", "error": "博客加载失败，请稍后再试。", "no_posts": "当前语言还没有文章，欢迎常来看看～", "read_more": "阅读全文", "published_on": "发布时间", "not_found": "没有找到这篇文章", "not_found_description": "你找的文章可能被删掉或不存在。", "error_description": "加载文章时遇到点问题，请稍后重试。", "back_to_home": "回到首页", "back": "返回", "page_title": "博客", "all_posts": "全部博客", "filter_by_tags": "按标签筛选", "all_tags": "全部标签", "no_tags": "暂无标签", "loading_tags": "标签加载中…", "no_posts_for_tags": "没有找到符合条件的文章。", "try_other_tags": "可以换个标签或者清空筛选看看～", "clear_filters": "清空筛选", "posts_count": "{count} 篇文章", "posts_count_singular": "1 篇文章", "selected_tags": "已选标签", "no_posts_audience_language": "当前语言没有适合这个身份的文章～", "try_different_language": "可以换个语言，看看还有什么内容。", "viewing_as": "当前浏览身份"}}