{"common": {"required": "Required fields", "submit": "Submit", "submitting": "Submitting...", "success": "Success!", "error": "Error", "warning": "Warning", "loading": "Loading...", "select_placeholder": "Please select...", "language": "Language", "optional": "optional", "yes": "Yes", "no": "No", "remove": "Remove"}, "navigation": {"home": "Home", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "toggle_hiring": "I'm <PERSON>ring", "toggle_job_seeking": "Get a job", "toggle_hiring_short": "<PERSON>re", "toggle_job_seeking_short": "Jobs", "blog": "Blog"}, "hero": {"title": "Built for workers.", "title_powered": "Powered by AI.", "subtitle": "Skip the paperwork. Land that job.", "subtitle_2": "Fast, simple, and in your language.", "cta_worker": "Join for Free", "cta_company": "Hire Talent", "trust_indicator": "2,500+ workers", "employer": {"title": "Find top talent.", "title_powered": "Powered by AI.", "subtitle": "Connect with 1,000+ verified blue‑collar professionals in your area, with our dedicated support every step of the way.", "subtitle_2": "Fast, reliable, and quality-verified.", "cta_worker": "Post Job Opening", "cta_company": "Learn More"}}, "features": {"title": "Why <PERSON><PERSON>?", "subtitle": "We make job matching simple and effective", "section_title": "Why <PERSON><PERSON>?", "section_subtitle": "Discover how our AI-powered platform revolutionizes job searching and hiring", "feature1_title": "You chat, We do the rest", "feature1_description": "<PERSON><PERSON><PERSON> listens to your voice, understands what you're good at, and creates your own profile.", "feature2_title": "Match to the right jobs", "feature2_description": "No need to search for hours. Our AI finds the best matching jobs and even applies for you.", "feature3_title": "A better future", "feature3_description": "With more experience, we show you better-paying jobs, more stable hours, and roles with more responsibility.", "feature4_title": "Professional Support", "feature4_description": "Get expert guidance throughout your job search journey with personalized career advice and interview preparation.", "employer": {"title": "Why Employers <PERSON><PERSON>?", "subtitle": "We make hiring skilled workers simple and effective", "feature1_title": "Smart Candidate Matching", "feature1_description": "Our AI analyzes job requirements and worker profiles to find the perfect match for your specific needs.", "feature2_title": "Rapid Hiring Process", "feature2_description": "Reduce your time-to-hire from weeks to days. Get qualified candidates in your pipeline within 24 hours.", "feature3_title": "Pre-Verified Workers", "feature3_description": "All workers undergo thorough background checks and skill verification before joining our platform.", "feature4_title": "Analytics & Insights", "feature4_description": "Make data-driven hiring decisions with comprehensive reporting and analytics on your recruitment performance."}}, "forms": {"title": "Get Started Today", "subtitle": "Choose the right option for you: Are you looking for a job or are you a company seeking qualified professionals?", "tab_worker": "I'm looking for a job", "tab_company": "We're hiring", "required_note": "Required fields", "footer_agreement": "By submitting this form, you agree to our", "and": "and"}, "chatbot": {"title": "Job Assistant", "chat_with_us": "Chat with us", "chat": "Cha<PERSON>"}, "worker_form": {"title": "Job Seeker Application", "subtitle": "Fill out the following fields. We will automatically create your resume from this information.", "success_title": "Application submitted successfully!", "success_description": "We'll get back to you soon. You will also receive a confirmation email shortly.", "personal_info": "Personal Information", "job_expectations": "Job Expectations", "work_history": "Work History", "skills_qualifications": "Skills & Qualifications", "fields": {"first_name": "First Name", "last_name": "Last Name", "email": "Email Address", "phone": "Phone Number", "connect_whatsapp": "Connect via WhatsApp", "facebook": "Facebook Account", "connect_facebook": "Connect via Facebook", "wechat": "<PERSON><PERSON><PERSON> Account", "connect_wechat": "Join WeChat Group", "city": "Current City", "nationality": "Nationality", "avatar": "Profile Photo", "availability_date": "Availability Date", "willing_to_travel": "Willing to Travel", "salary_expectation": "Salary Expectation (€/month)", "desired_position": "Desired Position", "employment_type": "Employment Type", "job_title": "Job Title", "job_company": "Company", "job_start": "Start Date", "job_end": "End Date", "job_duration": "Duration", "job_tasks": "Main Tasks & Responsibilities", "job1_title": "Most Recent Position", "job1_company": "Most Recent Company", "job1_duration": "Duration", "job1_tasks": "Main Tasks & Responsibilities", "education_level": "Education Level", "education_start": "Start Date", "education_end": "End Date", "education_school": "School/University", "education_detail": "Degree/Program Details", "german_level": "German Language Level", "other_languages": "Other Languages", "driving_license": "Driving License", "skills_certs": "Additional Skills & Certificates"}, "job_entry": "Job Experience", "add_job": "Add Another Job", "education_entry": "Education", "add_education": "Add Another Education", "education_details": "Education Details", "avatar_help": "Upload a professional photo (max 2MB, JPG/PNG)", "avatar_required": "Profile photo is required", "avatar_invalid_format": "Please upload a valid image file (JPG, PNG)", "avatar_too_large": "Image file must be smaller than 2MB", "avatar_too_small": "Image file is too small. Please upload a valid image", "placeholders": {"first_name": "Enter your first name", "last_name": "Enter your last name", "email": "<EMAIL>", "phone": "+49 **********", "facebook": "facebook.com/yourprofile or @yourhandle", "wechat": "Your WeChat ID", "city": "e.g. Berlin", "nationality": "e.g. German, Chinese, Polish", "willing_to_travel": "Select travel preference", "salary_expectation": "e.g. 2500", "desired_position": "e.g. Warehouse Worker", "employment_type": "Select employment type", "job_title": "e.g. Order Picker", "job_company": "e.g. Amazon Logistics", "job_duration": "e.g. 2 years", "job_tasks": "Describe your main responsibilities... (max 300 chars)", "job1_title": "e.g. Order Picker", "job1_company": "e.g. Amazon Logistics", "job1_duration": "e.g. 2 years", "job1_tasks": "Describe your main responsibilities... (max 300 chars)", "education_level": "Select your education level", "education_school": "e.g. University of Berlin", "education_detail": "e.g. Bachelor of Computer Science, Master of Engineering... (max 200 chars)", "german_level": "Select your German level", "other_languages": "e.g. English (C1), Spanish (A2) (max 100 chars)", "driving_license": "Select your license type", "skills_certs": "e.g. Welding Certificate, SCC Certificate, MS Office... (max 250 chars)", "availability_date": "Select your availability date", "job_start_month": "Select start month", "job_end_month": "Select end month", "education_start_month": "Select start month", "education_end_month": "Select end month"}, "options": {"employment_type": {"full_time": "Full-time", "part_time": "Part-time", "contract": "Contract", "temporary": "Temporary", "internship": "Internship"}, "education_level": {"no_degree": "No degree", "vocational_training": "Vocational training", "high_school": "High school", "bachelors_degree": "Bachelor's degree", "masters_degree": "Master's degree", "phd": "PhD"}, "german_level": {"a1": "A1 (<PERSON><PERSON>ner)", "a2": "A2 (Elementary)", "b1": "B1 (Intermediate)", "b2": "B2 (Upper Intermediate)", "c1": "C1 (Advanced)", "c2": "C2 (Proficient)", "native_speaker": "Native Speaker"}, "driving_license": {"none": "No license", "class_b": "Class B (Car)", "class_c1": "Class C1 (Small Truck)", "class_ce": "Class CE (Truck with Trailer)", "forklift_license": "Forklift License"}}}, "team": {"title": "Meet Our Team", "subtitle": "We're a diverse group of innovators, engineers, and career experts dedicated to revolutionizing how people find meaningful work.", "hero_title": "Innovation Through Collaboration", "hero_subtitle": "Our team works together to build the future of work, one connection at a time.", "members": {"roger": {"name": "<PERSON>", "role": "CEO & Founder", "bio": "With a background in engineering and management, <PERSON> is building Sohnus to make job matching faster, fairer, and easier—using AI to connect workers and companies without language barriers."}, "bijun": {"name": "Dr. <PERSON><PERSON><PERSON>", "role": "CTO & Co-founder", "bio": "With a Doctor of Engineering degree and extensive experience in advanced systems, <PERSON><PERSON><PERSON> leads <PERSON><PERSON><PERSON>'s technical vision. She builds and scales the AI platform that powers automatic job matching—bridging language gaps and connecting workers and employers efficiently."}, "yuanwei": {"name": "<PERSON><PERSON> Fang", "role": "CPO & Co-founder", "bio": "<PERSON><PERSON> combines deep UX expertise with hands-on experience in crafting AI chat experiences. She leads <PERSON><PERSON><PERSON>'s technical design, making sure our job-matching interface is simple, helpful—and built for real users."}}}, "footer": {"company_description": "Making meaningful employment accessible to every blue-collar worker through AI-powered job matching.", "job_seekers": "For Job Seekers", "employers": "For Employers", "contact_info": "Contact Info", "legal": "Legal", "apply_now": "Apply Now", "post_jobs": "Post Jobs", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "copyright": "© 2025 Sohnus. All rights reserved."}, "company_form": {"title": "Company Inquiry", "subtitle": "Tell us about your company and staffing requirements", "success_title": "Inquiry submitted successfully!", "success_description": "We'll get back to you soon. You will also receive a confirmation email shortly.", "fields": {"company_name": "Company Name", "contact_person": "Contact Person", "contact_email": "Contact Email", "contact_phone": "Contact Phone", "company_website": "Company Website", "needed_positions": "Needed Positions", "number_of_vacancies": "Number of Vacancies", "work_location": "Work Location", "required_skills": "Required Skills", "employment_model": "Employment Model", "urgency": "Urgency", "job_description": "Job Description"}, "placeholders": {"company_name": "Your company name", "contact_person": "Full name of contact person", "contact_email": "<EMAIL>", "contact_phone": "+49 **********", "company_website": "https://www.company.com", "needed_positions": "e.g. Warehouse Worker, Forklift Driver", "number_of_vacancies": "e.g., 5", "work_location": "e.g., Berlin, Germany or Remote", "required_skills": "e.g. Forklift license, German B2 level, Physical fitness", "employment_model": "Select employment type", "urgency": "Select urgency level", "job_description": "Detailed description of the job responsibilities, work environment, benefits, and any other relevant information..."}, "options": {"employment_model": {"full_time": "Full-time", "part_time": "Part-time", "contract": "Contract", "temporary": "Temporary", "internship": "Internship"}, "urgency": {"immediate": "Immediate (within 1 week)", "urgent": "<PERSON><PERSON> (within 2-4 weeks)", "normal": "Normal (within 1-2 months)", "flexible": "Flexible (when right candidate found)"}}}, "welcome_modal": {"text_over_image": "Start your journey here", "title": "Welcome to <PERSON><PERSON><PERSON>", "description": "Choose your path to get started with our AI-powered job platform", "job_seeker_button": "Looking for a Job", "hiring_button": "I'm <PERSON>ring", "additional_info": "You can always change this later in your profile settings", "close_button": "Close modal"}, "blog": {"latest_posts": "Latest Blog Posts", "view_all": "View all", "loading": "Loading blog posts...", "error": "Unable to load blog posts at the moment. Please try again later.", "no_posts": "No blog posts available in your language yet.", "read_more": "Read more", "published_on": "Published on", "not_found": "Blog Post Not Found", "not_found_description": "The blog post you're looking for doesn't exist or has been removed.", "error_description": "Something went wrong while loading the blog post. Please try again later.", "back_to_home": "Back to Home", "back": "Back", "page_title": "Blog", "all_posts": "All Blog Posts", "filter_by_tags": "Filter by tags", "all_tags": "All Tags", "no_tags": "No tags available", "loading_tags": "Loading tags...", "no_posts_for_tags": "No blog posts found for the selected tags.", "try_other_tags": "Try selecting different tags or clear filters to see all posts.", "clear_filters": "Clear filters", "posts_count": "{count} posts", "posts_count_singular": "1 post", "selected_tags": "Selected tags", "no_posts_audience_language": "No blog posts available for this audience in the current language.", "try_different_language": "Try switching to a different language to see more content.", "viewing_as": "Viewing as"}}