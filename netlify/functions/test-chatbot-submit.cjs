const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

exports.handler = async (event) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    // Test the chatbot contact submission logic
    const nocoDBUrl = process.env.NOCODB_URL;
    const nocoDBToken = process.env.NOCODB_TOKEN;
    const brevoApiKey = process.env.BREVO_API_KEY;
    const notificationEmail = process.env.NOTIFICATION_EMAIL;

    console.log('=== CHATBOT NOCODB TEST ===');
    console.log('This test validates:');
    console.log('1. Chatbot contact submissions to NocoDB');
    console.log('2. Different contact methods (WhatsApp, WeChat)');
    console.log('3. Multi-language support (English, Chinese)');
    console.log('4. Update vs Create logic for existing chatbot records');
    console.log('5. Proper Status field handling (WhatsappConnect, WechatConnect)');
    console.log('6. Source field tracking (Chatbot)');
    console.log('');
    console.log('Environment variables check:', {
      hasNocoDBUrl: !!nocoDBUrl,
      hasNocoDBToken: !!nocoDBToken,
      hasBrevoApiKey: !!brevoApiKey,
      hasNotificationEmail: !!notificationEmail,
      nocoDBUrl: nocoDBUrl,
      notificationEmail: notificationEmail
    });

    // Check if environment variables are set
    if (!nocoDBUrl || !nocoDBToken) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Missing NocoDB environment variables',
          details: {
            hasNocoDBUrl: !!nocoDBUrl,
            hasNocoDBToken: !!nocoDBToken,
            hasBrevoApiKey: !!brevoApiKey,
            hasNotificationEmail: !!notificationEmail
          }
        })
      };
    }

    // Test scenarios for different chatbot contact submissions
    const testScenarios = [
      {
        name: 'WhatsApp Contact (English)',
        data: {
          FirstName: "",
          LastName: "",
          PhoneNumber: "+49 176 12345678",
          Wechat_ZH: "",
          ConnectWhatsapp: "yes",
          ConnectWechat_ZH: "no",
          Status: "WhatsappConnect",
          Source: "Chatbot",
          Language: "en",
          formType: "worker"
        }
      },
      {
        name: 'WeChat Contact (Chinese)',
        data: {
          FirstName: "",
          LastName: "",
          PhoneNumber: "",
          Wechat_ZH: "test_wechat_123",
          ConnectWhatsapp: "no",
          ConnectWechat_ZH: "yes",
          Status: "WechatConnect",
          Source: "Chatbot",
          Language: "zh",
          formType: "worker"
        }
      },
      {
        name: 'Both WhatsApp and WeChat (Chinese)',
        data: {
          FirstName: "",
          LastName: "",
          PhoneNumber: "+49 176 87654321",
          Wechat_ZH: "test_wechat_456",
          ConnectWhatsapp: "yes",
          ConnectWechat_ZH: "yes",
          Status: "WhatsappConnect", // Primary contact method
          Source: "Chatbot",
          Language: "zh",
          formType: "worker"
        }
      }
    ];

    const results = [];

    for (const scenario of testScenarios) {
      console.log(`\n--- Testing: ${scenario.name} ---`);
      
      try {
        // Choose the appropriate table ID (worker table)
        const nocoTableId = 'mcv0hz2nurqap37';
        const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
        const nocoApiUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records`;

        console.log('Submitting to NocoDB:', {
          url: nocoApiUrl,
          tableId: nocoTableId,
          status: scenario.data.Status,
          source: scenario.data.Source,
          language: scenario.data.Language,
          hasPhone: !!scenario.data.PhoneNumber,
          hasWechat: !!scenario.data.Wechat_ZH
        });

        // Test chatbot contact submission logic
        // First, try to find existing chatbot record
        const searchUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records?where=(FirstName,eq,Chatbot)~and(LastName,eq,User)~and(Source,eq,Chatbot)`;
        
        console.log('Searching for existing chatbot record...');
        const searchResponse = await fetch(searchUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'xc-token': nocoDBToken,
          },
        });

        let isUpdate = false;
        let recordId = null;

        if (searchResponse.ok) {
          const searchData = await searchResponse.json();
          console.log('Search response:', {
            status: searchResponse.status,
            recordCount: searchData.list ? searchData.list.length : 0
          });

          if (searchData.list && searchData.list.length > 0) {
            // Found existing record, update it
            recordId = searchData.list[0].Id;
            isUpdate = true;
            console.log('Found existing chatbot record, will update:', recordId);
          }
        }

        let nocoResponse;
        
        if (isUpdate && recordId) {
          // Update existing record
          const updateUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records`;
          nocoResponse = await fetch(updateUrl, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'xc-token': nocoDBToken,
            },
            body: JSON.stringify([{
              Id: recordId,
              ...scenario.data
            }]),
          });
        } else {
          // Create new record
          nocoResponse = await fetch(nocoApiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'xc-token': nocoDBToken,
            },
            body: JSON.stringify(scenario.data),
          });
        }

        const responseData = await nocoResponse.json();
        
        const result = {
          scenario: scenario.name,
          success: nocoResponse.ok,
          status: nocoResponse.status,
          operation: isUpdate ? 'UPDATE' : 'CREATE',
          recordId: recordId,
          response: nocoResponse.ok ? 'Success' : responseData,
          timestamp: new Date().toISOString()
        };

        console.log('Result:', result);
        results.push(result);

      } catch (error) {
        console.error(`Error in scenario ${scenario.name}:`, error);
        results.push({
          scenario: scenario.name,
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Summary
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    console.log('\n=== TEST SUMMARY ===');
    console.log(`Successful: ${successCount}/${totalCount}`);
    console.log('All results:', results);

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Chatbot NocoDB test completed',
        summary: {
          total: totalCount,
          successful: successCount,
          failed: totalCount - successCount
        },
        results: results,
        timestamp: new Date().toISOString()
      }, null, 2)
    };

  } catch (error) {
    console.error('Test error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
