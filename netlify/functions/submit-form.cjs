const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

exports.handler = async (event) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // 1. Accept only POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ message: 'Only POST requests allowed' })
    };
  }

  try {
    // Check if body exists
    if (!event.body) {
      console.error('Request body is missing');
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ message: 'Request body is missing' })
      };
    }

    let formData;
    try {
      formData = JSON.parse(event.body);
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError.message);
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ message: 'Invalid JSON in request body' })
      };
    }

    const formType = formData.formType;

    console.log('Received form submission:', {
      formType,
      dataKeys: Object.keys(formData),
      bodyLength: event.body.length,
      timestamp: new Date().toISOString()
    });

    // 2. Check if the form type is valid
    if (formType !== 'worker' && formType !== 'company') {
      console.log('Invalid form type:', formType);
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ message: 'Invalid form type specified.' })
      };
    }

    // --- Step A: Save to NocoDB ---
    const nocoDBUrl = process.env.NOCODB_URL;
    const nocoDBToken = process.env.NOCODB_TOKEN;

    // Check if environment variables are set
    if (!nocoDBUrl || !nocoDBToken) {
      console.error('Missing NocoDB environment variables:', {
        nocoDBUrl: !!nocoDBUrl,
        nocoDBToken: !!nocoDBToken,
        actualUrl: nocoDBUrl ? nocoDBUrl.substring(0, 50) + '...' : 'undefined'
      });
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          message: 'Server configuration error - NocoDB credentials missing',
          error: 'Missing NocoDB environment variables'
        })
      };
    }

    // Choose the appropriate table ID based on form type
    const nocoTableId = formType === 'worker' ? 'mcv0hz2nurqap37' : 'm2yux6be57tujg8';

    // Ensure the URL doesn't have a trailing slash before adding the API path
    const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
    // Use the correct API v2 structure with table ID
    const nocoApiUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records`;

    console.log('Attempting to save to NocoDB:', {
      nocoDBUrl: nocoDBUrl ? nocoDBUrl.substring(0, 50) + '...' : 'undefined',
      baseUrl,
      nocoApiUrl,
      tableType: formType,
      tableId: nocoTableId,
      dataKeys: Object.keys(formData),
      status: formData.Status,
      timestamp: new Date().toISOString()
    });

    let nocoResponse;
    let recordId;
    let isUpdate = false;

    // For contact submissions (WhatsappConnect, FacebookConnect, WechatConnect),
    // try to find existing record and update it
    if (formType === 'worker' && formData.Status &&
        ['WhatsappConnect', 'FacebookConnect', 'WechatConnect'].includes(formData.Status)) {

      // Try to find existing chatbot record to update
      try {
        const searchUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records?where=(FirstName,eq,Chatbot)~and(LastName,eq,User)~and(Source,eq,Chatbot)`;
        const searchResponse = await fetch(searchUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'xc-token': nocoDBToken,
          },
        });

        if (searchResponse.ok) {
          const searchResult = await searchResponse.json();
          if (searchResult.list && searchResult.list.length > 0) {
            // Update existing record
            const existingRecord = searchResult.list[0];
            recordId = existingRecord.Id;
            const updateUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records/${recordId}`;

            nocoResponse = await fetch(updateUrl, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'xc-token': nocoDBToken,
              },
              body: JSON.stringify(formData),
            });
            isUpdate = true;
            console.log('Updating existing chatbot record:', recordId);
          }
        }
      } catch (searchError) {
        console.warn('Failed to search for existing record, creating new one:', searchError.message);
      }
    }

    // If not an update, create new record
    if (!isUpdate) {
      try {
        nocoResponse = await fetch(nocoApiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'xc-token': nocoDBToken,
          },
          body: JSON.stringify(formData),
        });
      } catch (fetchError) {
        console.error('NocoDB fetch error:', {
          error: fetchError.message,
          stack: fetchError.stack,
          url: nocoApiUrl
        });
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({
            message: 'Failed to connect to database',
            error: 'Network error while saving data'
          })
        };
      }
    }

    if (!nocoResponse.ok) {
      // If NocoDB fails to store, log the error and return
      const errorText = await nocoResponse.text();
      console.error('NocoDB Error:', {
        status: nocoResponse.status,
        statusText: nocoResponse.statusText,
        response: errorText,
        url: nocoApiUrl,
        requestData: JSON.stringify(formData).substring(0, 200) + '...'
      });
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          message: 'Failed to save data to database',
          error: `Database error: ${nocoResponse.status} ${nocoResponse.statusText}`,
          details: errorText.substring(0, 200)
        })
      };
    }

    const nocoResponseData = await nocoResponse.text();
    console.log('NocoDB success:', {
      status: nocoResponse.status,
      response: nocoResponseData.substring(0, 200) + '...'
    });

    // Parse the response to get the record ID (if not already set from update)
    if (!recordId) {
      try {
        const parsedResponse = JSON.parse(nocoResponseData);
        recordId = parsedResponse.Id || parsedResponse.id;
        console.log('Extracted record ID from response:', recordId);
      } catch (parseError) {
        console.warn('Failed to parse NocoDB response for record ID:', parseError.message);
      }
    } else {
      console.log('Using existing record ID from update:', recordId);
    }

    // --- Step B: Send Brevo notification email ---
    const brevoApiKey = process.env.BREVO_API_KEY;
    const notificationEmail = process.env.NOTIFICATION_EMAIL; // This is your own email address

    console.log('Email configuration check:', {
      hasBrevoApiKey: !!brevoApiKey,
      hasNotificationEmail: !!notificationEmail,
      notificationEmail: notificationEmail
    });

    if (!brevoApiKey || !notificationEmail) {
      console.warn('Email configuration missing - skipping email notification');
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          message: 'Form submitted successfully! (Email notification skipped due to configuration)',
          recordId: recordId
        })
      };
    }

    // Prepare email content
    const emailSubject = formType === 'worker'
      ? `New worker form: ${formData.FirstName} ${formData.LastName}`
      : `New company form: ${formData.CompanyName}`;

    const emailHtmlContent = `<p>A new form has been submitted via the ${formType.toUpperCase()} form.</p><pre>${JSON.stringify(formData, null, 2)}</pre>`;

    console.log('Sending email notification:', {
      subject: emailSubject,
      to: notificationEmail,
      timestamp: new Date().toISOString()
    });

    let brevoResponse;
    try {
      brevoResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': brevoApiKey,
        },
        body: JSON.stringify({
          sender: { email: '<EMAIL>', name: 'Sohnus Form' }, // Make sure this email is verified in Brevo
          to: [{ email: notificationEmail }],
          subject: emailSubject,
          htmlContent: emailHtmlContent,
        }),
      });
    } catch (emailFetchError) {
      console.error('Brevo fetch error:', emailFetchError);
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          message: 'Form submitted successfully! (Email notification failed to send)',
          recordId: recordId
        })
      };
    }

    if (!brevoResponse.ok) {
      // If email sending fails, log the error but still return success since data was saved
      const errorText = await brevoResponse.text();
      console.error('Brevo Error:', {
        status: brevoResponse.status,
        statusText: brevoResponse.statusText,
        response: errorText
      });
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          message: 'Form submitted successfully! (Email notification failed to send)',
          recordId: recordId
        })
      };
    }

    const brevoResponseData = await brevoResponse.text();
    console.log('Email sent successfully:', {
      status: brevoResponse.status,
      response: brevoResponseData.substring(0, 100) + '...'
    });

    // --- Step C: Send acknowledgment email to user ---
    // Write down the template IDs you get from Brevo here!
    const templateMap = {
      worker: { // Worker
        de: 1, // German worker template ID
        en: 2, // English worker template ID
        zh: 4, // Chinese worker template ID
        es: 3, // Spanish worker template ID
      },
      company: { // Company
        de: 5, // German company template ID
        en: 8, // English company template ID
        zh: 7, // Chinese company template ID
        es: 6, // Spanish company template ID
      }
    };

    const language = formData.Language || formData.language || 'en'; // If language is not provided, default to English
    const templateId = templateMap[formType]?.[language];

    console.log('Language detection debug:', {
      formDataLanguage: formData.Language,
      formDataLanguageLowercase: formData.language,
      detectedLanguage: language,
      formType,
      templateId,
      availableTemplates: templateMap[formType]
    });

    // If a template ID is found, send ACK email to user
    if (templateId) {
      const userEmail = formData.Email; // Get user email from form data

      if (userEmail) {
        // Prepare dynamic parameters for the template
        const dynamicParams = formType === 'worker'
          ? { FirstName: formData.FirstName }
          : { CompanyName: formData.CompanyName };

        console.log('Sending acknowledgment email to user:', {
          userEmail,
          templateId,
          language,
          formType,
          dynamicParams,
          formDataLanguage: formData.Language,
          formDataLanguageLowercase: formData.language,
          availableTemplates: templateMap[formType],
          timestamp: new Date().toISOString()
        });

        try {
          // Call Brevo API to send template email
          const ackEmailResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'api-key': brevoApiKey,
            },
            body: JSON.stringify({
              to: [{ email: userEmail }],
              templateId: templateId, // Use template ID
              params: dynamicParams,  // Pass dynamic parameters
            }),
          });

          if (ackEmailResponse.ok) {
            const ackResponseData = await ackEmailResponse.text();
            console.log('Acknowledgment email sent successfully:', {
              status: ackEmailResponse.status,
              response: ackResponseData.substring(0, 100) + '...',
              userEmail
            });
          } else {
            const ackErrorText = await ackEmailResponse.text();
            console.error('Failed to send acknowledgment email:', {
              status: ackEmailResponse.status,
              statusText: ackEmailResponse.statusText,
              response: ackErrorText,
              userEmail
            });
          }
        } catch (ackEmailError) {
          console.error('Error sending acknowledgment email:', {
            error: ackEmailError.message,
            userEmail,
            templateId
          });
        }
      } else {
        console.warn('No user email found in form data - skipping acknowledgment email');
      }
    } else {
      console.warn('No template found for acknowledgment email:', {
        formType,
        language,
        availableTemplates: templateMap
      });
    }

    // --- Success! ---
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Form submitted successfully!',
        recordId: recordId
      })
    };

  } catch (error) {
    console.error('Error processing form:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        message: 'An internal error occurred while processing your form.',
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
