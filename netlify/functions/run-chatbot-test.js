const testChatbotSubmit = require('./test-chatbot-submit.cjs');

async function runChatbotTest() {
  console.log('Running chatbot NocoDB submission test...');
  
  const mockEvent = {
    httpMethod: 'GET',
    headers: {},
    body: null
  };

  try {
    const result = await testChatbotSubmit.handler(mockEvent);
    console.log('Test result:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Test failed:', error);
  }
}

runChatbotTest();
